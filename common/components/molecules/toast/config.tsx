'use client'

import { 
  <PERSON><PERSON><PERSON>he<PERSON>, BadgeMinus,
} from "lucide-react";

export const toasterConfig = {
  toastOptions: {
    duration: 5000,
    success: {
      icon: <span className="mr-2 text-green-300"><BadgeCheck /></span>,
    },
    error: {
      icon: <span className="mr-2 text-tulip"><BadgeMinus /></span>,
    },
  },
  containerStyle: {
    right: 16,
    top: 40,
    bottom: 52,
  },
}
