'use client'

import React, {
  useState, useEffect,
  useCallback,
} from 'react';
import Kon<PERSON> from 'konva';
import { 
  motion, AnimatePresence,
} from 'framer-motion';
import {
  X,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  ChevronUp,
  ChevronDown,
  Trash2,
  Type,
  Image as ImageIcon,
  Square,
  Circle,
} from 'lucide-react';

interface LayersPanelProps {
    canvas: Konva.Stage | null;
    onClose: () => void;
}

interface LayerInfo {
    object: Konva.Node;
    id: string;
    name: string;
    type: string;
    visible: boolean;
    locked: boolean;
    selected: boolean;
}

export const LayersPanel = ({
  canvas, onClose,
}: LayersPanelProps) => {
  const [layers, setLayers] = useState<LayerInfo[]>([]);

  const getObjectIcon = (type: string) => {
    switch (type) {
    case 'text':
    case 'i-text':
      return <Type size={14} />;
    case 'image':
      return <ImageIcon size={14} />;
    case 'rect':
      return <Square size={14} />;
    case 'circle':
      return <Circle size={14} />;
    default:
      return <Square size={14} />;
    }
  };

  const getObjectName = (obj: Konva.Node, index: number) => {
    const className = obj.getClassName();
    if (className === 'Text') {
      const text = (obj as any).text() || '';
      return text.length > 20 ? `${text.substring(0, 20)}...` : text || `Text ${index + 1}`;
    }
    if (className === 'Image') {
      return `Image ${index + 1}`;
    }
    return `${className} ${index + 1}`;
  };

  const updateLayers = useCallback(() => {
    if (!canvas) {
      return;
    }

    const allObjects: Konva.Node[] = [];
    const transformer = canvas.findOne('Transformer') as Konva.Transformer;
    const selectedNodes = transformer?.nodes() || [];

    canvas.children.forEach((layer: any) => {
      if (layer.children) {
        layer.children.forEach((child: Konva.Node) => {
          if (child.getClassName() !== 'Transformer') {
            allObjects.push(child);
          }
        });
      }
    });

    const layerInfos: LayerInfo[] = allObjects.map((obj: Konva.Node, index: number) => ({
      object: obj,
      id: `layer-${index}`,
      name: getObjectName(obj, index),
      type: obj.getClassName().toLowerCase(),
      visible: obj.visible(),
      locked: !obj.draggable(),
      selected: selectedNodes.includes(obj),
    })).reverse();

    setLayers(layerInfos);
  }, [canvas]);

  useEffect(() => {
    if (!canvas) {
      return;
    }

    updateLayers();

    const handleChange = () => updateLayers();

    canvas.on('click', handleChange);
    canvas.on('tap', handleChange);
    canvas.on('dragend', handleChange);
    canvas.on('transformend', handleChange);

    return () => {
      canvas.off('click', handleChange);
      canvas.off('tap', handleChange);
      canvas.off('dragend', handleChange);
      canvas.off('transformend', handleChange);
    };
  }, [canvas, updateLayers]);

  const selectLayer = (layer: LayerInfo) => {
    if (!canvas) {
      return;
    }

    const layerNode = layer.object.getLayer();
    if (!layerNode) {
      return;
    }

    let transformer = layerNode.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layerNode.add(transformer);
    }

    transformer.nodes([layer.object]);
    canvas.batchDraw();
    updateLayers();
  };

  const toggleVisibility = (layer: LayerInfo) => {
    if (!canvas) {
      return;
    }
    layer.object.visible(!layer.object.visible());
    canvas.batchDraw();
    updateLayers();
  };

  const toggleLock = (layer: LayerInfo) => {
    if (!canvas) {
      return;
    }
    layer.object.draggable(!layer.object.draggable());
    canvas.batchDraw();
    updateLayers();
  };

  const moveLayerUp = (layer: LayerInfo) => {
    if (!canvas) {
      return;
    }
    layer.object.moveUp();
    canvas.batchDraw();
    updateLayers();
  };

  const moveLayerDown = (layer: LayerInfo) => {
    if (!canvas) {
      return;
    }
    layer.object.moveDown();
    canvas.batchDraw();
    updateLayers();
  };

  const deleteLayer = (layer: LayerInfo) => {
    if (!canvas) {
      return;
    }
    layer.object.destroy();
    canvas.batchDraw();
    updateLayers();
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ 
          x: '100%',
          opacity: 0,
        }}
        animate={{ 
          x: 0, 
          opacity: 1,
        }}
        exit={{ 
          x: '100%', 
          opacity: 0,
        
        }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30,
        }}
        className="fixed top-[60px] right-0 z-40 w-80 h-[calc(100vh-104px)] bg-neutral-800/95 backdrop-blur-sm border-l border-neutral-600 flex flex-col"
      >
        <div className="flex items-center justify-between px-3 py-2 border-b border-neutral-600 flex-shrink-0">
          <h3 className="font-medium text-gray-200">Layers</h3>
          <button
            onClick={onClose}
            className="p-1 rounded hover:bg-neutral-700 transition-colors"
          >
            <X size={16} className="text-gray-400" />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {layers.length === 0 ? (
            <div className="p-4 text-center text-gray-400 text-sm">
              No layers yet. Add some content to see layers here.
            </div>
          ) : (
            <div className="p-2 space-y-1">
              {layers.map((layer) => (
                <div
                  key={layer.id}
                  className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-colors ${
                    layer.selected
                      ? 'bg-violets-are-blue/20 border border-violets-are-blue/40'
                      : 'hover:bg-neutral-700 border border-transparent'
                  }`}
                  onClick={() => selectLayer(layer)}
                >
                  <div className="text-gray-300">
                    {getObjectIcon(layer.type)}
                  </div>
                  <div className="flex-1 text-sm text-gray-200 truncate">
                    {layer.name}
                  </div>
                  <div className="flex items-center gap-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        moveLayerUp(layer);
                      }}
                      className="p-1 rounded-lg hover:bg-violets-are-blue/15 transition-colors"
                      title="Move Up"
                    >
                      <ChevronUp size={16} className="text-gray-400" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        moveLayerDown(layer);
                      }}
                      className="p-1 rounded-lg hover:bg-violets-are-blue/15 transition-colors"
                      title="Move Down"
                    >
                      <ChevronDown size={16} className="text-gray-400" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleVisibility(layer);
                      }}
                      className="p-1 rounded-lg hover:bg-violets-are-blue/15 transition-colors"
                      title={layer.visible ? 'Hide' : 'Show'}
                    >
                      {layer.visible ? (
                        <Eye size={16} className="text-gray-400" />
                      ) : (
                        <EyeOff size={16} className="text-gray-400" />
                      )}
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleLock(layer);
                      }}
                      className="p-1 rounded-lg hover:bg-violets-are-blue/15 transition-colors"
                      title={layer.locked ? 'Unlock' : 'Lock'}
                    >
                      {layer.locked ? (
                        <Lock size={16} className="text-gray-400" />
                      ) : (
                        <Unlock size={16} className="text-gray-400" />
                      )}
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteLayer(layer);
                      }}
                      className="p-1 rounded-lg hover:bg-violets-are-blue/15 transition-colors"
                      title="Delete"
                    >
                      <Trash2 size={16} className="text-gray-400" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
