'use client'

import React, {
  useState,
} from 'react';
import Konva from 'konva';
import {
  Undo2,
  Redo2,
  ZoomIn,
  ZoomOut,
  Layers,
  HelpCircle,
} from 'lucide-react';
import { secondaryFont } from '@/common/utils/localFont';
import { HelpModal } from './HelpModal';

interface FloatingToolbarProps {
  canvas: Konva.Stage | null;
  zoomLevel: number;
  onZoomChange: (zoom: number) => void;
  onFitToView: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  showLayers?: boolean;
  onToggleLayers?: () => void;
}

export const FloatingToolbar = ({
  zoomLevel,
  onZoomChange,
  onFitToView,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  showLayers = false,
  onToggleLayers,
}: FloatingToolbarProps) => {
  const [showHelp, setShowHelp] = useState(false);

  const handleUndo = () => {
    if (onUndo && canUndo) {
      onUndo();
    }
  };

  const handleRedo = () => {
    if (onRedo && canRedo) {
      onRedo();
    }
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoomLevel * 1.2, 1.25);
    onZoomChange(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoomLevel / 1.2, 0.1);
    onZoomChange(newZoom);
  };

  const zoomPercentage = Math.round(zoomLevel * 100);

  return (
    <>
      <div className="flex items-center justify-center gap-1 rounded-lg p-1">
        <button
          onClick={handleUndo}
          disabled={!canUndo}
          className="p-2 rounded-lg hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Undo"
        >
          <Undo2 size={16} className="text-gray-300" />
        </button>
        <button
          onClick={handleRedo}
          disabled={!canRedo}
          className="p-2 rounded-lg hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Redo"
        >
          <Redo2 size={16} className="text-gray-300" />
        </button>
        <div className="w-px h-6 bg-neutral-500 mx-1" />
        <button
          onClick={handleZoomOut}
          className="p-2 rounded-lg hover:bg-neutral-600 transition-colors"
          title="Zoom Out"
        >
          <ZoomOut size={16} className="text-gray-300" />
        </button>
        <button
          onClick={onFitToView}
          className={`px-3 py-1 text-sm font-medium text-gray-300 hover:bg-neutral-600 rounded-lg transition-colors min-w-[60px] ${secondaryFont.className}`}
          title="Fit to View"
        >
          {zoomPercentage}%
        </button>
        <button
          onClick={handleZoomIn}
          className="p-2 rounded-lg hover:bg-neutral-600 transition-colors"
          title="Zoom In"
        >
          <ZoomIn size={16} className="text-gray-300" />
        </button>
        <div className="w-px h-6 bg-neutral-500 mx-1" />
        <button
          onClick={onToggleLayers}
          className={`p-2 rounded-lg transition-colors ${
            showLayers ? 'bg-violets-are-blue text-white' : 'hover:bg-neutral-600 text-gray-300'
          }`}
          title="Layers"
        >
          <Layers size={16} />
        </button>
        <button
          onClick={() => setShowHelp(true)}
          className="p-2 rounded-lg hover:bg-neutral-600 transition-colors"
          title="Help"
        >
          <HelpCircle size={16} className="text-gray-300" />
        </button>
      </div>

      <HelpModal
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
      />
    </>
  );
};
