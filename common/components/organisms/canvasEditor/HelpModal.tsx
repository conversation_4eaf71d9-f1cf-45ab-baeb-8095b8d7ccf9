'use client'

import React from 'react';
import {
  Mouse, Layers, Wand2, Upload, Type, Sparkles, History,
  Shapes,
} from 'lucide-react';
import { DefaultModal } from '../modal/defaultModal';

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const HelpModal = ({
  isOpen,
  onClose,
}: HelpModalProps) => {
  const shortcuts = [
    {
      key: 'Delete',
      description: 'Delete selected objects',
    },
    {
      key: 'Ctrl/Cmd + Z',
      description: 'Undo last action',
    },
    {
      key: 'Ctrl/Cmd + Y',
      description: 'Redo last action',
    },
    {
      key: 'Double Click',
      description: 'Edit text inline',
    },
  ];

  const features = [
    {
      icon: <Wand2 size={20} />,
      title: 'Generate Images',
      description: 'Create AI-generated images with custom prompts and styles',
    },
    {
      icon: <Upload size={20} />,
      title: 'Upload Images',
      description: 'Import your own images and photos to the canvas',
    },
    {
      icon: <Type size={20} />,
      title: 'Add Text',
      description: 'Insert and customize text elements with various fonts',
    },
    {
      icon: <Sparkles size={20} />,
      title: 'Improve Images',
      description: 'Enhance your canvas design with AI improvements',
    },
    {
      icon: <Shapes size={20} />,
      title: 'Vector Images',
      description: 'Generate scalable vector graphics for logos and icons',
    },
    {
      icon: <History size={20} />,
      title: 'Recent Uploads',
      description: 'Access your recently generated and uploaded images',
    },
    {
      icon: <Layers size={20} />,
      title: 'Layer Management',
      description: 'Organize, reorder, and control visibility of canvas elements',
    },
    {
      icon: <Mouse size={20} />,
      title: 'Object Controls',
      description: 'Resize, rotate, and position objects with precision',
    },
  ];

  const tipsSections = [
    {
      title: 'Text Editing',
      icon: <Type size={16} />,
      tips: [
        'Select text on canvas to modify style & alignment',
        'Double-click text to edit content',
        'Press shift+enter to add new line',
        'Use Delete key to remove selected text',
      ],
    },
    {
      title: 'Image Generation',
      icon: <Wand2 size={16} />,
      tips: [
        'Higher guidance scale = more adherence to your prompt',
        'Different seeds produce different variations',
        'Be specific in your prompts for better results',
      ],
    },
  ];

  return (
    <DefaultModal
      isOpen={isOpen}
      onClose={onClose}
      maxWidth="md:max-w-4xl"
      maxHeight="max-h-[85vh]"
    >
      <div className="px-6 pb-4">
        <div className="mb-6">
          <h2 className="text-2xl font-semibold text-white text-left">Canvas Editor Help</h2>
          <p className="text-gray-400 text-sm mt-1 text-left">Learn how to use the canvas editor effectively</p>
        </div>

        <div className="space-y-8">
          <div>
            <h3 className="text-lg font-medium text-left text-white mb-4">Features</h3>
            <div className="grid grid-cols-3 gap-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center flex-col gap-3 px-3 py-6 rounded-xl bg-neutral-700">
                  <div>
                    <h4 className="font-semibold text-base text-white">{feature.title}</h4>
                    <p className="text-sm text-gray-400 mt-2">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-left text-white mb-4">Tips & Tricks</h3>
            <div className="grid grid-cols-2 gap-4">
              {tipsSections.map((section, index) => (
                <div key={index} className="p-4 rounded-xl bg-neutral-700 text-center">
                  <div className="mb-3">
                    <h4 className="font-semibold text-base text-white">{section.title}</h4>
                  </div>
                  <ul className="space-y-1">
                    {section.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="text-sm text-gray-400">
                        • {tip}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-left text-white mb-4">Keyboard Shortcuts</h3>
            <div className="space-y-3">
              {shortcuts.map((shortcut, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-neutral-700">
                  <span className="text-sm text-gray-400">{shortcut.description}</span>
                  <kbd className="px-2 py-1 text-xs font-mono bg-neutral-600 border border-neutral-500 rounded text-gray-300">
                    {shortcut.key}
                  </kbd>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </DefaultModal>
  );
};
